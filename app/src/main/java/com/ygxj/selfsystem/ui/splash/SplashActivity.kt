package com.ygxj.selfsystem.ui.splash

import android.view.View
import com.ygxj.active.ActivateUtil
import com.ygxj.base.VMBaseActivity
import com.ygxj.base.util.DialogUtil
import com.ygxj.data.HostManager
import com.ygxj.selfsystem.BuildConfig
import com.ygxj.selfsystem.R
import com.ygxj.selfsystem.databinding.ActivitySplashBinding
import com.ygxj.selfsystem.ui.main.MainActivity
import kotlin.system.exitProcess

class SplashActivity : VMBaseActivity<ActivitySplashBinding, SplashModel>() {

    override fun createViewModel() = SplashModel()

    override fun getLayoutId() = R.layout.activity_splash

    override fun initData() {
        binding.model = model

        setObserve()
        model.checkData()
    }

    private fun setObserve() {
        // 资源解压失败,要退出程序
        model.decompressFailFlag.observe(this) { v ->
            if (!v) return@observe
            DialogUtil.showDialogWithConfirmCallBack(this, "资源解压失败，请检查设备是否有足够的空间，并确保程序安装包完整", "退出程序") { dialog, _ ->
                dialog.dismiss()
                exitProcess(0)
            }
        }
        model.checkDataFinished.observe(this) { value ->
            if (!value) {
                binding.llProgress.visibility = View.VISIBLE
                binding.llLogo.visibility = View.GONE
                return@observe
            }
            model.progress.value = 1f
            binding.llProgress.visibility = View.GONE
            binding.llLogo.visibility = View.VISIBLE
            binding.llLogo.postDelayed({
                // 检测设备授权状态
                if (BuildConfig.DEBUG) {
                    goToNextPage()
                } else {
                    ActivateUtil.getInstance(this).doActivate { goToNextPage() }
                }
            }, 500)
        }
    }

    /**
     * 根据状态,跳转至MainActivity或ConfigActivity
     */
    private fun goToNextPage() {
        if (HostManager.host.isBlank()) {
            DialogConfigHost(this) {
                MainActivity.start(this)
                finish()
            }.build().show()
            return
        }
        MainActivity.start(this)
        finish()
    }
}