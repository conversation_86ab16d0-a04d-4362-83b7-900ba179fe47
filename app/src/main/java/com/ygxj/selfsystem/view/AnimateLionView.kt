package com.ygxj.selfsystem.view

import android.content.Context
import android.util.AttributeSet
import androidx.appcompat.widget.AppCompatImageView
import com.bumptech.glide.Glide
import com.bumptech.glide.load.DataSource
import com.bumptech.glide.load.engine.GlideException
import com.bumptech.glide.load.resource.gif.GifDrawable
import com.bumptech.glide.request.RequestListener
import com.bumptech.glide.request.target.Target
import com.ygxj.selfsystem.R

class AnimateLionView(
    context: Context,
    attrs: AttributeSet
) : AppCompatImageView(context, attrs) {

    private var gifDrawable: GifDrawable? = null

    init {
        Glide.with(context)
            .asGif()
            .load(R.drawable.lion)
            .listener(object : RequestListener<GifDrawable> {
                override fun onLoadFailed(e: GlideException?, model: Any?, target: Target<GifDrawable?>?, isFirstResource: Boolean): Boolean {
                    return false
                }

                override fun onResourceReady(
                    resource: GifDrawable?,
                    model: Any?,
                    target: Target<GifDrawable?>?,
                    dataSource: DataSource?,
                    isFirstResource: Boolean
                ): Boolean {
                    resource?.let {
                        gifDrawable = it
                        it.stop()
                    }
                    return false
                }
            })
            .into(this)
    }

    fun play() {
        gifDrawable?.let {
            if (!it.isRunning) {
                it.start()
            }
        }
    }

    fun pause() {
        gifDrawable?.let {
            if (it.isRunning) {
                it.stop()
            }
        }
    }

}