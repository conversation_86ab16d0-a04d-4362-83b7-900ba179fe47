<?xml version="1.0" encoding="utf-8"?>
<layout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data />

    <com.drake.statelayout.StateLayout
        android:id="@+id/stateLayout"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        tools:context=".ui.main.center.CenterBriefIntroFragment">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:layout_weight="0.2"
                android:orientation="horizontal">

                <com.ygxj.selfsystem.view.AnimateLionView
                    android:id="@+id/vAnimateLion"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content" />
            </LinearLayout>

            <com.hjq.shape.layout.ShapeFrameLayout
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:layout_weight="0.6"
                android:padding="10dp"
                app:shape_radius="4dp"
                app:shape_solidColor="#B3FFFFFF"
                app:shape_strokeColor="#80144872"
                app:shape_strokeWidth="1dp">

                <WebView
                    android:id="@+id/webView"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:scrollbars="none" />

            </com.hjq.shape.layout.ShapeFrameLayout>

            <View
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:layout_weight="0.2" />
        </LinearLayout>

    </com.drake.statelayout.StateLayout>
</layout>